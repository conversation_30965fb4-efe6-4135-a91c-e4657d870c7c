import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule, ReactiveFormsModule, FormGroup, FormControl, Validators } from '@angular/forms';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { Local } from '@app/core/models/local';
import { GenericTableComponent } from '@app/components/generic-table/generic-table.component';
import { CardLocalComponent } from '@app/components/card-local/card-local.component';
import { LocalApiService, TypeLocalApiService } from '@app/core/services/administrative/local.service';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatIconModule } from '@angular/material/icon';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { Site } from '@app/core/models/site';
import { FilterParam, Lister, Page } from '@app/core/models/util/page';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';
import { NgxUiLoaderModule, NgxUiLoaderService } from 'ngx-ui-loader';
import { HotToastService } from '@ngxpert/hot-toast';

interface IndexableLocal extends Local {
  [key: string]: any;
}

@Component({
  selector: 'app-local-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    GenericTableComponent,
    CardLocalComponent,
    MatPaginatorModule,
    MatIconModule,
    MatDialogModule,
    MatSnackBarModule,
    NgxUiLoaderModule,
    // NgxSpinnerModule
  ],
  templateUrl: './local-management.component.html',
  styleUrls: ['./local-management.component.css'],
  animations: [
    trigger('tableRowAnimation', [
      state('void', style({ opacity: 0, transform: 'translateY(20px)' })),
      transition('void => *', animate('300ms ease-in'))
    ]),
    trigger('fadeIn', [
      state('void', style({ opacity: 0 })),
      transition('void => *', animate('400ms 300ms ease-in'))
    ])
  ]
})
export class LocalManagementComponent implements OnInit {

  constructor(
    private readonly localService: LocalApiService,
    private readonly typeLocalService: TypeLocalApiService,
    private readonly siteService: SiteApiService,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly snackBar: MatSnackBar,
    private readonly dialog: MatDialog,
    private readonly ngxUiLoaderService: NgxUiLoaderService,
    private readonly  toast: HotToastService
    // private readonly spinner: NgxSpinnerService
  ) {}


  locals: Local[] = [];
  filteredLocals: IndexableLocal[] = [];
  sites: any[] = [];
  searchTerm: string = '';
  isLoading: boolean = true;
  showCreateForm: boolean = false;
  showEditForm: boolean = false;
  uploadedImages: File[] = [];
  uploadedImages2D: File[] = [];
  viewMode: 'cards' | 'table' = 'cards';
  currentPage: number = 0;
  pageSize: number = 5;
  totalCount: number = 0;
  paginatedLocals: Local[] = [];
  isSubmitting: boolean = false;
  siteId: string | null = null;
  selectedLocal: Local | null = null;
  currentSite: any | null = null;
  showDashboard: boolean = false;

  typeLocals: any[] = [];

  headers: string[] = ['Nom', 'Étage', 'Capteur', 'Capacité', 'Type'];
  keys: string[] = ['Name', 'Floor', 'SensorsCount', 'Capacity', 'TypeLocal.Nom'];

  createLocalForm = new FormGroup({
    Name: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    BaseTopicMQTT: new FormControl(''),
    Floor: new FormControl<number>(0),
    SensorsCount: new FormControl<number>(0),
    Capacity: new FormControl<number>(0),
    Architecture2DImage: new FormControl(''),
    ImageLocal: new FormControl(''),
    Latitude: new FormControl<number>(0),
    Longtitude: new FormControl<number>(0),
    TypeLocalId: new FormControl('', [Validators.required]),
    IdSite: new FormControl('', [Validators.required])
  });

  editLocalForm = new FormGroup({
    Name: new FormControl('', [Validators.required]),
    Description: new FormControl(''),
    BaseTopicMQTT: new FormControl(''),
    Floor: new FormControl<number>(0),
    SensorsCount: new FormControl<number>(0),
    Capacity: new FormControl<number>(0),
    Architecture2DImage: new FormControl(''),
    ImageLocal: new FormControl(''),
    Latitude: new FormControl<number>(0),
    Longtitude: new FormControl<number>(0),
    TypeLocalId: new FormControl('', [Validators.required]),
    IdSite: new FormControl('', [Validators.required])
  });

  ngOnInit(): void {
    this.loadLocals();
    this.loadSiteDetails();
    this.loadSites();
    this.loadTypeLocals();

    this.route.queryParams.subscribe(params => {
      if (params['action'] === 'create') {
        this.showAddLocalForm();
      }
    });
  }

  loadSiteDetails(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    if (siteId) {
      this.siteService.getById(siteId).subscribe({
        next: (site) => {
          this.currentSite = site;
        },
        error: (error) => {
          console.error('Error loading site details:', error);
        }
      });
    }
  }
  loadTypeLocals(): void {
    this.typeLocalService.getAll().subscribe({
      next: (types) => {
        this.typeLocals = types;
      },
      error: (error) => {
        console.error('Error loading type locals:', error);
        this.typeLocals = [];
      }
    });
  }

  toggleViewMode(): void {
    this.viewMode = this.viewMode === 'cards' ? 'table' : 'cards';
    this.updatePaginatedLocals();
  }

  updatePaginatedLocals(): void {
    const startIndex = this.currentPage * this.pageSize;
    this.paginatedLocals = this.filteredLocals.slice(startIndex, startIndex + this.pageSize);
  }

  loadSites(): void {
    this.isLoading = true;
    this.siteService.getAll().subscribe({
      next: (sites) => {
        this.sites = sites;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading sites:', error);
        this.isLoading = false;
      }
    });
  }

  getSiteNameById(siteId: string | null | undefined): string {
    if (!siteId) return '';
    const site = this.sites.find(s => s.Id === siteId || s.id === siteId);
    if (!site) return '';
    return (site.Name || site.name) + (site.Adress || site.adress ? ' (' + (site.Adress || site.adress) + ')' : '');
  }

    loadLocals(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    this.isLoading = true;
    this.ngxUiLoaderService.start(); // Start the loader
    // this.spinner.show();
  
    const request: Lister = {
      pagination: {
        currentPage: this.currentPage + 1,
        pageSize: this.pageSize,
        totalElement: 0
      },
      filterParams: [
        {
          column: 'IdSite',
          value: siteId ?? '',
          op: 'eq'
        }
      ]
    };
  
    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        // Process images before assigning to locals
        this.locals = (response.Content ?? []).map(local => ({
          ...local,
          // Convert string images to proper base64 format if they exist
          Architecture2DImage: local.Architecture2DImage ? 
            this.ensureBase64Format(local.Architecture2DImage) : '',
          ImageLocal: local.ImageLocal ? 
            this.ensureBase64Format(local.ImageLocal) : ''
        }));
        this.filteredLocals = this.locals;
        this.currentSite.EmployeesCount=this.locals.length;
        this.siteService.update(this.currentSite).subscribe({
          next: (site) => {
            this.currentSite = site;
          },
          error: (error) => {
            console.error('Error updating site:', error);
          }
        });
        this.totalCount = response.Lister?.pagination?.totalElement ?? 0;
        this.updatePaginatedLocals();
        this.isLoading = false;
        this.ngxUiLoaderService.stop(); // Stop the loader
      },
      error: (error) => {
        console.error('Error loading locals:', error);
        this.showErrorNotification('Erreur lors du chargement des locaux');
        this.filteredLocals = [];
        this.totalCount = 0;
        this.isLoading = false;
        this.ngxUiLoaderService.stop(); // Stop the loader
      }
    });
  }
  
  // Add helper method to ensure proper base64 format
  private ensureBase64Format(imageData: string): string {
    // Remove any existing data:image prefix if present
    const base64Data = imageData.includes('base64,') ? 
      imageData.split('base64,')[1] : imageData;
    
    // Clean up any whitespace or invalid characters
    return base64Data.trim();
  }
  
  // Update submitEditForm method
  submitEditForm(): void {
    if (this.editLocalForm.valid && this.selectedLocal) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Confirmer la modification',
          message: 'Voulez-vous vraiment modifier ce local ?',
          icon: 'edit'
        }
      });
  
      dialogRef.afterClosed().subscribe(result => {
        if (result && this.selectedLocal) {
          this.isSubmitting = true;
          const formValues = this.editLocalForm.value;
  
          const updatedLocal: Local = {
            Id: this.selectedLocal.Id,
            CreatedAt: this.selectedLocal.CreatedAt,
            CreatedBy: this.selectedLocal.CreatedBy,
            LastUpdatedAt: new Date(),
            LastUpdatedBy: 'system',
            Name: formValues.Name || this.selectedLocal.Name,
            BaseTopicMQTT: formValues.BaseTopicMQTT || this.selectedLocal.BaseTopicMQTT,
            Floor: Number(formValues.Floor) || this.selectedLocal.Floor,
            SensorsCount: Number(formValues.SensorsCount) || this.selectedLocal.SensorsCount,
            Capacity: Number(formValues.Capacity) || this.selectedLocal.Capacity,
            Architecture2DImage: this.ensureBase64Format(formValues.Architecture2DImage || this.selectedLocal.Architecture2DImage || ''),
            ImageLocal: this.ensureBase64Format(formValues.ImageLocal || this.selectedLocal.ImageLocal || ''),
            Description: formValues.Description || this.selectedLocal.Description,
            IdSite: formValues.IdSite || this.selectedLocal.IdSite,
            TypeLocalId: formValues.TypeLocalId || this.selectedLocal.TypeLocalId,
            Latitude: Number(formValues.Latitude) || this.selectedLocal.Latitude,
            Longtitude: Number(formValues.Longtitude) || this.selectedLocal.Longtitude
          };
  
          this.localService.update(updatedLocal).subscribe({
            next: () => {
              this.loadLocals();
              this.hideEditLocalForm();
              this.isSubmitting = false;
              this.toast.success('Local modifié avec succès !');
            },
            error: (error) => {
              console.error('Update error:', error);
              this.toast.error('Erreur lors de la modification du local');
              this.isSubmitting = false;
            }
          });
        }
      });
    }
  }

  onPageChange(event: any): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadLocals();
  }

  filterLocals(): void {
    const siteId = this.route.snapshot.paramMap.get('siteId');
    const searchTerm = this.searchTerm.trim();

    const filters: FilterParam[] = [];

    if (siteId) {
      filters.push({
        column: 'IdSite',
        value: siteId,
        op: 'eq'
      });
    }

    if (searchTerm) {
      const searchableFields = ['Name'];
      searchableFields.forEach((field, idx) => {
        filters.push({
          column: field,
          value: searchTerm,
          op: 'contains',
          andOr: idx === 0 ? 'AND' : 'OR'
        });
      });
    }

    const request: Lister = {
      pagination: {
        currentPage: this.currentPage + 1,
        pageSize: this.pageSize,
        totalElement: 0
      },
      filterParams: filters
    };

    this.isLoading = true;
    this.ngxUiLoaderService.start(); 

    this.localService.gatePage(request).subscribe({
      next: (response: Page<Local>) => {
        this.filteredLocals = response.Content ?? [];
        this.totalCount = response.Lister?.pagination?.totalElement ?? 0;
        this.updatePaginatedLocals();
        this.isLoading = false;
        this.ngxUiLoaderService.stop(); 
        // this.spinner.hide();
      },

      error: (error) => {
        console.error('Error during backend filtering:', error);
        this.showErrorNotification('Erreur serveur — affichage complet sans filtre.');
        this.loadLocals();
      }
    });
  }

  generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  submitCreateForm(): void {
    if (this.createLocalForm.valid) {
      const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
        data: {
          title: 'Confirmer la création',
          message: 'Voulez-vous vraiment créer ce local ?',
          icon: 'add'
        }
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.isSubmitting = true;
          const formValues = this.createLocalForm.value;
          const local: Local = {
            Id: this.generateUUID(),
            Name: formValues.Name ?? '',
            Description: formValues.Description ?? '',
            BaseTopicMQTT: formValues.BaseTopicMQTT ?? '',
            Floor: formValues.Floor ?? 0,
            SensorsCount: formValues.SensorsCount ?? 0,
            Capacity: formValues.Capacity ?? 0,
            Architecture2DImage: formValues.Architecture2DImage ?? '',
            ImageLocal: formValues.ImageLocal ?? '',
            IdSite: formValues.IdSite ?? '',
            Latitude: 0,
            Longtitude: 0,
            TypeLocalId: formValues.TypeLocalId ?? ''
          };

          this.localService.create(local).subscribe({
            next: () => {
              this.loadLocals();
              this.hideAddLocalForm();
              this.isSubmitting = false;
              this.toast.success('Local créé avec succès !');
            },
            error: (error) => {
              this.isSubmitting = false;
              this.toast.error('Erreur lors de la création du local');
            }
          });
        }
      });
    }
  }

  deleteLocal(id: string): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: 'Confirmer la suppression',
        message: 'Voulez-vous vraiment supprimer ce local ?',
        icon: 'delete'
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.localService.delete(id).subscribe({
          next: () => {
            this.loadLocals();
            this.toast.success('Local supprimé avec succès !');
          },
          error: (error) => {
            this.toast.error('Erreur lors de la suppression du local');
          }
        });
      }
    });
  }

  viewDetails(id: string): void {
    this.router.navigate(['/local-details/', id]);
  }

  showAddLocalForm(): void {
    this.showCreateForm = true;
    this.createLocalForm.reset();
    const siteIdFromRoute = this.route.snapshot.paramMap.get('siteId');
    let siteIdToSet = '';
    if (siteIdFromRoute) {
      siteIdToSet = siteIdFromRoute;
    } else if (this.sites && this.sites.length > 0) {
      siteIdToSet = this.sites[0].Id || this.sites[0].id;
    }
    if (siteIdToSet) {
      this.createLocalForm.patchValue({ IdSite: siteIdToSet });
    }
  }

  hideAddLocalForm(): void {
    this.showCreateForm = false;
  }

  showEditLocalForm(local: Local): void {
    this.selectedLocal = local;
    this.editLocalForm.patchValue({
      Name: local.Name,
      Description: local.Description,
      BaseTopicMQTT: local.BaseTopicMQTT,
      Floor: local.Floor,
      SensorsCount: local.SensorsCount,
      Capacity: local.Capacity,
      Architecture2DImage: local.Architecture2DImage,
      ImageLocal: local.ImageLocal,
      TypeLocalId: local.TypeLocalId,
      Longtitude: local.Longtitude,
      Latitude: local.Latitude,
      IdSite: local.IdSite
    });
    this.showEditForm = true;
  }

  hideEditLocalForm(): void {
    this.showEditForm = false;
    this.selectedLocal = null;
  }
  
  
  clearSearch(): void {
    this.searchTerm = '';
    this.filterLocals();
  }

  editLocal(local: Local): void {
    this.showEditLocalForm(local);
  }

  onImagesSelected(event: any, fieldName: string): void {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64Data = result.includes('base64,')
          ? result.split('base64,')[1]
          : result;

        if (this.showCreateForm) {
          this.createLocalForm.patchValue({
            [fieldName]: base64Data
          });
        } else if (this.showEditForm && this.selectedLocal) {
          this.editLocalForm.patchValue({
            [fieldName]: base64Data
          });
        }
      };
      reader.readAsDataURL(file);
    }
  }

  handleAction(event: { action: string; row: any }): void {
    const { action, row } = event;
    if (action === 'edit') {
      this.editLocal(row);
    } else if (action === 'delete') {
      this.deleteLocal(row.Id);
    } else if (action === 'view') {
      this.viewDetails(row.Id);
    }
  }

  private showSuccessNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 4000,
      panelClass: ['success-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  private showErrorNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  private showInfoNotification(message: string): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['info-snackbar'],
      horizontalPosition: 'right',
      verticalPosition: 'top'
    });
  }

  goBackToOrganisation(): void {
    if (this.currentSite?.ClientId) {
      this.router.navigate(['/organisation-details', this.currentSite.ClientId]);
    } else {
      this.router.navigate(['/organisations']);
    }
  }

  toggleDashboard(): void {
    this.showDashboard = !this.showDashboard;
  }
}
